import { ReactNode } from 'react';
import { Header } from './header';

interface MainLayoutProps {
  children: ReactNode;
  user?: {
    id: number;
    username: string;
    email?: string;
  };
}

export function MainLayout({ children, user }: MainLayoutProps) {
  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      <Header user={user} />
      <main className="flex-1 overflow-hidden">
        {children}
      </main>
    </div>
  );
}
