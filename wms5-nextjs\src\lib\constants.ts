// API 配置
export const API_CONFIG = {
  BASE_URL: 'http://gizmodelphine.ddns.net:8081/api/v2.0',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// API 端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/accesstoken',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
  },
  
  // 产品相关
  PRODUCTS: {
    LIST: '/products',
    CREATE: '/products',
    UPDATE: '/products',
    DELETE: (id: number) => `/products/${id}`,
    GET_BY_ID: (id: number) => `/products/${id}`,
    STOCK: (id: number) => `/products/${id}/stock`,
    IMAGES: '/products/images',
    USER_PRICES: (id: number) => `/products/${id}/userprices`,
    DISALLOWED_GROUPS: (id: number) => `/products/${id}/disallowedusergroups`,
    PURCHASE_AVAILABILITY: (id: number) => `/products/${id}/purchaseavailability`,
  },
  
  // 产品组相关
  PRODUCT_GROUPS: {
    LIST: '/productgroups',
    CREATE: '/productgroups',
    UPDATE: '/productgroups',
    DELETE: (id: number) => `/productgroups/${id}`,
  },
  
  // 用户组相关
  USER_GROUPS: {
    LIST: '/usergroups',
  },
} as const;

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Cookie 配置
export const COOKIE_CONFIG = {
  AUTH_TOKEN: 'auth-token',
  MAX_AGE: 24 * 60 * 60 * 1000, // 24小时
  SECURE: process.env.NODE_ENV === 'production',
  HTTP_ONLY: true,
  SAME_SITE: 'lax' as const,
} as const;

// 表单验证规则
export const VALIDATION_RULES = {
  PRODUCT_NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 255,
  },
  PRICE: {
    MIN: 0,
    MAX: 999999.99,
  },
  STOCK: {
    MIN: 0,
    MAX: 999999,
  },
  BARCODE: {
    MAX_LENGTH: 50,
  },
} as const;

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const;

// 图片上传配置
export const IMAGE_CONFIG = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  DIMENSIONS: {
    WIDTH: 400,
    HEIGHT: 400,
  },
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接错误，请检查网络设置',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器内部错误，请稍后重试',
  VALIDATION_ERROR: '输入数据验证失败',
  UNKNOWN_ERROR: '未知错误，请联系管理员',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出登录成功',
  PRODUCT_CREATED: '产品创建成功',
  PRODUCT_UPDATED: '产品更新成功',
  PRODUCT_DELETED: '产品删除成功',
  IMAGE_UPLOADED: '图片上传成功',
} as const;

// 本地存储键名
export const STORAGE_KEYS = {
  FORM_STATE: 'product-form-state',
  SEARCH_HISTORY: 'search-history',
  USER_PREFERENCES: 'user-preferences',
} as const;

// 时间格式
export const DATE_FORMATS = {
  DISPLAY: 'YYYY-MM-DD HH:mm:ss',
  API: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE_ONLY: 'YYYY-MM-DD',
  TIME_ONLY: 'HH:mm',
} as const;

// 产品状态
export const PRODUCT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  OUT_OF_STOCK: 'out_of_stock',
  DISCONTINUED: 'discontinued',
} as const;

// 库存状态类
export const STOCK_STATUS_CLASSES = {
  IN_STOCK: 'text-green-600 bg-green-50',
  LOW_STOCK: 'text-yellow-600 bg-yellow-50',
  OUT_OF_STOCK: 'text-red-600 bg-red-50',
  DISABLED: 'text-gray-600 bg-gray-50',
} as const;
