import { NextRequest, NextResponse } from 'next/server';
import { getAuthHeaders } from '@/lib/auth';
import { API_CONFIG, API_ENDPOINTS, HTTP_STATUS } from '@/lib/constants';

// GET /api/productsstock - 获取产品库存
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('ProductId');

    if (!productId) {
      return NextResponse.json({ error: 'ProductId is required' }, { status: 400 });
    }

    // 构建API URL，使用路径参数而不是查询参数
    const apiUrl = `${API_CONFIG.BASE_URL}/productsstock/${productId}`;

    console.log('Fetching product stock from:', apiUrl);
    console.log('Request headers:', await getAuthHeaders());
    
    // 获取认证头
    const authHeaders = await getAuthHeaders();
    
    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: authHeaders,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log('API error response:', response.status, errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const data = await response.json();
    console.log('Product stock API response:', JSON.stringify(data, null, 2));

    return NextResponse.json(data, { status: HTTP_STATUS.OK });

  } catch (error: any) {
    console.error('Get product stock error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '获取产品库存失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限访问产品库存';
    } else if (error.message?.includes('404')) {
      statusCode = HTTP_STATUS.NOT_FOUND;
      errorMessage = '产品库存不存在';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// PUT /api/productsstock - 修改产品库存
export async function PUT(request: Request) {
  try {
    const { id, type, amount } = await request.json();

    if (!id || type === undefined || !amount) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // 构建API URL
    const apiUrl = `${API_CONFIG.BASE_URL}/productsstock`;

    console.log('Updating product stock:', { id, type, amount });
    console.log('Request headers:', await getAuthHeaders());

    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: {
        ...(await getAuthHeaders()),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id, type, amount }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Stock update API error response:', response.status, errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const data = await response.json();
    console.log('Stock update API response:', JSON.stringify(data, null, 2));

    return NextResponse.json(data, { status: HTTP_STATUS.OK });

  } catch (error: any) {
    console.error('Update stock error:', error);

    return NextResponse.json(
      { error: '修改产品库存失败' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    );
  }
}
