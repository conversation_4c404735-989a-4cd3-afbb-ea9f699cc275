import { NextRequest, NextResponse } from 'next/server';
import { loginUser, setAuthCookie, handleAuthError } from '@/lib/auth';
import { HTTP_STATUS } from '@/lib/constants';
import type { LoginRequest, LoginResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    const { username, password } = body;

    // 验证输入
    if (!username || !password) {
      return NextResponse.json(
        { error: '用户名和密码不能为空' },
        { status: HTTP_STATUS.BAD_REQUEST }
      );
    }

    // 调用原始API进行登录
    const loginResponse: LoginResponse = await loginUser(username.trim(), password.trim());
    
    // 检查登录响应
    const token = loginResponse.result?.token;
    if (!token) {
      return NextResponse.json(
        { error: '登录失败：无有效token' },
        { status: HTTP_STATUS.UNAUTHORIZED }
      );
    }

    // 设置HttpOnly <PERSON>ie
    await setAuthCookie(token, loginResponse.result?.user);

    // 返回成功响应（不包含token，因为已存储在HttpOnly Cookie中）
    return NextResponse.json(
      {
        success: true,
        message: '登录成功',
        user: loginResponse.result?.user,
      },
      { status: HTTP_STATUS.OK }
    );

  } catch (error: any) {
    console.error('Login error:', error);
    
    const errorMessage = handleAuthError(error);
    
    // 根据错误类型返回相应的HTTP状态码
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    
    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
    } else if (error.message?.includes('400')) {
      statusCode = HTTP_STATUS.BAD_REQUEST;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
