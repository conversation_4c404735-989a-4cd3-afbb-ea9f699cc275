import { Metadata } from 'next';
import { getCurrentUser } from '@/lib/auth';
import { MainLayout } from '@/components/layout/main-layout';
import { ProductForm } from '@/components/products/product-form';

export const metadata: Metadata = {
  title: '添加产品 - 产品库存管理系统',
  description: '添加新产品到库存管理系统',
};

export default async function AddProductPage() {
  const user = await getCurrentUser();

  return (
    <MainLayout user={user || undefined}>
      <ProductForm />
    </MainLayout>
  );
}
