'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { AccordionItem } from '@/components/ui/accordion';
import type { Product, ProductGroup } from '@/types';

interface AdvancedEditModalProps {
  product: Product | null;
  productGroups: ProductGroup[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedProduct: Partial<Product>) => Promise<void>;
}

export function AdvancedEditModal({ 
  product, 
  productGroups, 
  isOpen, 
  onClose, 
  onSave 
}: AdvancedEditModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    cost: '',
    barcode: '',
    groupId: '',
    minimumStock: '',
    maximumStock: '',
    isStockDisabled: false,
    disallowSaleIfOutOfStock: false,
    stockAlert: false,
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        price: product.price?.toString() || '',
        cost: product.cost?.toString() || '',
        barcode: product.barcode || '',
        groupId: product.groupId?.toString() || '',
        minimumStock: product.minimumStock?.toString() || '',
        maximumStock: product.maximumStock?.toString() || '',
        isStockDisabled: product.isStockDisabled || false,
        disallowSaleIfOutOfStock: product.disallowSaleIfOutOfStock || false,
        stockAlert: product.stockAlert || false,
      });
    }
  }, [product]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!product) return;

    setIsSaving(true);
    try {
      const updatedProduct = {
        id: product.id,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        price: formData.price ? parseFloat(formData.price) : undefined,
        cost: formData.cost ? parseFloat(formData.cost) : undefined,
        barcode: formData.barcode.trim() || undefined,
        groupId: formData.groupId ? parseInt(formData.groupId) : undefined,
        minimumStock: formData.minimumStock ? parseInt(formData.minimumStock) : undefined,
        maximumStock: formData.maximumStock ? parseInt(formData.maximumStock) : undefined,
        isStockDisabled: formData.isStockDisabled,
        disallowSaleIfOutOfStock: formData.disallowSaleIfOutOfStock,
        stockAlert: formData.stockAlert,
      };

      await onSave(updatedProduct);
      onClose();
    } catch (error) {
      console.error('Advanced edit error:', error);
      alert('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级编辑产品: {product?.name}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            {/* 基本信息 */}
            <AccordionItem title="基本信息" defaultExpanded={true}>
              <div className="space-y-4 p-4">
                <div className="space-y-2">
                  <Label htmlFor="name">产品名称 *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">价格 (RM)</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cost">成本 (RM)</Label>
                    <Input
                      id="cost"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.cost}
                      onChange={(e) => handleInputChange('cost', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="barcode">条码</Label>
                  <Input
                    id="barcode"
                    value={formData.barcode}
                    onChange={(e) => handleInputChange('barcode', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="groupId">产品组</Label>
                  <Select
                    value={formData.groupId}
                    onValueChange={(value) => handleInputChange('groupId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择产品组" />
                    </SelectTrigger>
                    <SelectContent>
                      {productGroups.map((group) => (
                        <SelectItem key={group.id} value={group.id.toString()}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </AccordionItem>

            {/* 库存设置 */}
            <AccordionItem title="库存设置" defaultExpanded={false}>
              <div className="space-y-4 p-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isStockDisabled"
                    checked={formData.isStockDisabled}
                    onCheckedChange={(checked) => handleInputChange('isStockDisabled', checked as boolean)}
                  />
                  <Label htmlFor="isStockDisabled">禁用库存管理</Label>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minimumStock">最小库存</Label>
                    <Input
                      id="minimumStock"
                      type="number"
                      min="0"
                      value={formData.minimumStock}
                      onChange={(e) => handleInputChange('minimumStock', e.target.value)}
                      disabled={formData.isStockDisabled}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maximumStock">最大库存</Label>
                    <Input
                      id="maximumStock"
                      type="number"
                      min="0"
                      value={formData.maximumStock}
                      onChange={(e) => handleInputChange('maximumStock', e.target.value)}
                      disabled={formData.isStockDisabled}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="stockAlert"
                    checked={formData.stockAlert}
                    onCheckedChange={(checked) => handleInputChange('stockAlert', checked as boolean)}
                    disabled={formData.isStockDisabled}
                  />
                  <Label htmlFor="stockAlert">启用库存警告</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="disallowSaleIfOutOfStock"
                    checked={formData.disallowSaleIfOutOfStock}
                    onCheckedChange={(checked) => handleInputChange('disallowSaleIfOutOfStock', checked as boolean)}
                    disabled={formData.isStockDisabled}
                  />
                  <Label htmlFor="disallowSaleIfOutOfStock">缺货时禁止销售</Label>
                </div>
              </div>
            </AccordionItem>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? '保存中...' : '保存'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
