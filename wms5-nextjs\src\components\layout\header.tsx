'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Package, Plus, LogOut, User, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { logout } from '@/lib/api';

interface HeaderProps {
  user?: {
    id: number;
    username: string;
    email?: string;
  };
}

export function Header({ user }: HeaderProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Left: Logo and Title */}
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <h1 className="text-lg font-medium text-gray-900">产品库存管理</h1>
          </div>

          {/* Right: Action Buttons */}
          <div className="flex items-center space-x-3">
            <Link href="/products/add">
              <Button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 text-sm">
                添加产品
              </Button>
            </Link>
            <Button
              onClick={handleLogout}
              disabled={isLoading}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 text-sm"
            >
              {isLoading ? '退出中...' : '退出'}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
