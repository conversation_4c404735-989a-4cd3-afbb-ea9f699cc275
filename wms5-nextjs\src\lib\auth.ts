import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { COOKIE_CONFIG, API_CONFIG, API_ENDPOINTS } from './constants';
import type { AuthCookieData, User, LoginResponse } from '@/types';

/**
 * 设置认证 Cookie
 */
export async function setAuthCookie(token: string, user?: User): Promise<void> {
  const cookieStore = await cookies();
  
  const authData: AuthCookieData = {
    token,
    expiresAt: Date.now() + COOKIE_CONFIG.MAX_AGE,
    user,
  };

  cookieStore.set(COOKIE_CONFIG.AUTH_TOKEN, JSON.stringify(authData), {
    httpOnly: COOKIE_CONFIG.HTTP_ONLY,
    secure: COOKIE_CONFIG.SECURE,
    sameSite: COOKIE_CONFIG.SAME_SITE,
    maxAge: COOKIE_CONFIG.MAX_AGE / 1000, // Convert to seconds
    path: '/',
  });
}

/**
 * 获取认证 Cookie
 */
export async function getAuthCookie(): Promise<AuthCookieData | null> {
  try {
    const cookieStore = await cookies();
    const authCookie = cookieStore.get(COOKIE_CONFIG.AUTH_TOKEN);
    
    if (!authCookie?.value) {
      return null;
    }

    const authData: AuthCookieData = JSON.parse(authCookie.value);
    
    // 检查是否过期
    if (authData.expiresAt < Date.now()) {
      await clearAuthCookie();
      return null;
    }

    return authData;
  } catch (error) {
    console.error('Error parsing auth cookie:', error);
    await clearAuthCookie();
    return null;
  }
}

/**
 * 清除认证 Cookie
 */
export async function clearAuthCookie(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.delete(COOKIE_CONFIG.AUTH_TOKEN);
}

/**
 * 检查用户是否已认证
 */
export async function isAuthenticated(): Promise<boolean> {
  const authData = await getAuthCookie();
  return authData !== null && authData.token !== '';
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(): Promise<User | null> {
  const authData = await getAuthCookie();
  return authData?.user || null;
}

/**
 * 获取认证头
 */
export async function getAuthHeaders(): Promise<Record<string, string>> {
  const authData = await getAuthCookie();
  
  if (!authData?.token) {
    throw new Error('No authentication token found');
  }

  return {
    'Authorization': authData.token.startsWith('Bearer ') ? authData.token : `Bearer ${authData.token}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}

/**
 * 登录用户
 */
export async function loginUser(username: string, password: string): Promise<LoginResponse> {
  const endpoint = `${API_ENDPOINTS.AUTH.LOGIN}?Username=${encodeURIComponent(username)}&Password=${encodeURIComponent(password)}`;
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: LoginResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Login API request failed:', error);
    throw error;
  }
}

/**
 * 从请求中获取认证数据（用于中间件）
 */
export function getAuthFromRequest(request: NextRequest): AuthCookieData | null {
  try {
    const authCookie = request.cookies.get(COOKIE_CONFIG.AUTH_TOKEN);
    
    if (!authCookie?.value) {
      return null;
    }

    const authData: AuthCookieData = JSON.parse(authCookie.value);
    
    // 检查是否过期
    if (authData.expiresAt < Date.now()) {
      return null;
    }

    return authData;
  } catch (error) {
    console.error('Error parsing auth cookie from request:', error);
    return null;
  }
}

/**
 * 创建认证响应（用于中间件）
 */
export function createAuthResponse(request: NextRequest, redirectTo?: string): NextResponse {
  const response = redirectTo 
    ? NextResponse.redirect(new URL(redirectTo, request.url))
    : NextResponse.next();

  // 清除无效的认证 Cookie
  response.cookies.delete(COOKIE_CONFIG.AUTH_TOKEN);
  
  return response;
}

/**
 * 验证 token 格式
 */
export function isValidTokenFormat(token: string): boolean {
  return typeof token === 'string' && token.length > 0;
}

/**
 * 处理认证错误
 */
export function handleAuthError(error: any): string {
  if (error.message?.includes('401')) {
    return '用户名或密码错误';
  } else if (error.message?.includes('403')) {
    return '账户被禁用，请联系管理员';
  } else if (error.message?.includes('Network')) {
    return '网络连接错误，请检查网络设置';
  } else {
    return '登录失败，请稍后重试';
  }
}

/**
 * 刷新认证状态（延长 Cookie 有效期）
 */
export async function refreshAuth(): Promise<boolean> {
  try {
    const authData = await getAuthCookie();
    
    if (!authData) {
      return false;
    }

    // 重新设置 Cookie 以延长有效期
    await setAuthCookie(authData.token, authData.user);
    return true;
  } catch (error) {
    console.error('Error refreshing auth:', error);
    return false;
  }
}
