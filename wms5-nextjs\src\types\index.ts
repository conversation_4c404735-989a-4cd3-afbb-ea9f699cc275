// API 基础类型
export interface ApiResponse<T = any> {
  result?: T;
  success?: boolean;
  message?: string;
  error?: string;
}

// 用户认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  result?: {
    token: string;
    user?: User;
  };
}

export interface User {
  id: number;
  username: string;
  email?: string;
  role?: string;
}

// 产品相关类型
export interface Product {
  id: number;
  name: string;
  price?: number;
  cost?: number;
  barcode?: string;
  groupId?: number;
  groupName?: string;
  description?: string;
  isStockDisabled?: boolean;
  disallowSaleIfOutOfStock?: boolean;
  stockAlert?: boolean;
  minimumStock?: number;
  maximumStock?: number;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProductGroup {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  children?: ProductGroup[];
}

export interface ProductStock {
  productId: number;
  quantity: number;
  reserved?: number;
  available?: number;
  lastUpdated?: string;
}

export interface ProductImage {
  id?: number;
  productId: number;
  imageUrl: string;
  imageData?: string; // base64 encoded image
  isPrimary?: boolean;
  order?: number;
}

export interface ProductUserPrice {
  id?: number;
  productId: number;
  userId?: number;
  userGroupId?: number;
  price: number;
  startDate?: string;
  endDate?: string;
}

export interface ProductDisallowedUserGroup {
  id?: number;
  productId: number;
  userGroupId: number;
  reason?: string;
}

export interface ProductPurchaseAvailability {
  productId: number;
  isAvailable: boolean;
  availableFrom?: string;
  availableTo?: string;
  timeSlots?: TimeSlot[];
}

export interface TimeSlot {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isEnabled: boolean;
}

// 表单相关类型
export interface ProductFormData {
  name: string;
  price?: number;
  cost?: number;
  barcode?: string;
  groupId?: number;
  description?: string;
  enableStock: boolean;
  minimumStock?: number;
  maximumStock?: number;
  disallowSaleIfOutOfStock?: boolean;
  stockAlert?: boolean;
  image?: File;
  userPrices?: ProductUserPrice[];
  disallowedUserGroups?: number[];
  purchaseAvailability?: ProductPurchaseAvailability;
}

// 搜索和筛选类型
export interface ProductSearchParams {
  query?: string;
  groupId?: number;
  inStockOnly?: boolean;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'cost' | 'stock' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 用户组类型
export interface UserGroup {
  id: number;
  name: string;
  description?: string;
  permissions?: string[];
}

// API 错误类型
export interface ApiError {
  status: number;
  message: string;
  details?: any;
}

// 组件 Props 类型
export interface ProductCardProps {
  product: Product;
  stock?: number;
  onEdit?: (product: Product) => void;
  onDelete?: (productId: number) => void;
  onStockUpdate?: (productId: number, newStock: number) => void;
}

export interface ProductFormProps {
  product?: Product;
  onSubmit: (data: ProductFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (query: string) => void;
  placeholder?: string;
  isLoading?: boolean;
}

// 页面状态类型
export interface PageState {
  isLoading: boolean;
  error?: string;
  success?: string;
}

// Cookie 认证类型
export interface AuthCookieData {
  token: string;
  expiresAt: number;
  user?: User;
}

// 导出所有类型的联合类型，便于使用
export type ProductRelatedData = Product | ProductGroup | ProductStock | ProductImage | ProductUserPrice | ProductDisallowedUserGroup;
export type FormData = ProductFormData;
export type SearchData = ProductSearchParams;
