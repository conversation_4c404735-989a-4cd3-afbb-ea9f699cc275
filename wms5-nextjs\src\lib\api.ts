import { API_CONFIG, API_ENDPOINTS, HTTP_STATUS, ERROR_MESSAGES } from './constants';
import type { 
  ApiResponse, 
  Product, 
  ProductGroup, 
  ProductStock, 
  ProductImage,
  ProductUserPrice,
  ProductDisallowedUserGroup,
  ProductPurchaseAvailability,
  ProductSearchParams,
  ProductListResponse,
  UserGroup,
  ApiError
} from '@/types';

/**
 * API客户端类
 */
class ApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = '/api'; // 使用Next.js API路由作为代理
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // 自动发送cookies
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      // 检查响应是否为JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return response.text() as unknown as T;
      }
    } catch (error) {
      console.error('API request failed:', error);
      throw this.createApiError(error);
    }
  }

  /**
   * 处理错误响应
   */
  private async handleErrorResponse(response: Response): Promise<never> {
    let errorMessage = ERROR_MESSAGES.UNKNOWN_ERROR;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
    } catch {
      // 如果无法解析JSON，使用默认错误消息
      switch (response.status) {
        case HTTP_STATUS.UNAUTHORIZED:
          errorMessage = ERROR_MESSAGES.UNAUTHORIZED;
          break;
        case HTTP_STATUS.FORBIDDEN:
          errorMessage = ERROR_MESSAGES.FORBIDDEN;
          break;
        case HTTP_STATUS.NOT_FOUND:
          errorMessage = ERROR_MESSAGES.NOT_FOUND;
          break;
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
          errorMessage = ERROR_MESSAGES.SERVER_ERROR;
          break;
        default:
          errorMessage = ERROR_MESSAGES.NETWORK_ERROR;
      }
    }

    const apiError: ApiError = {
      status: response.status,
      message: errorMessage,
    };

    throw apiError;
  }

  /**
   * 创建API错误对象
   */
  private createApiError(error: any): ApiError {
    if (error.status && error.message) {
      return error as ApiError;
    }

    return {
      status: 0,
      message: error.message || ERROR_MESSAGES.NETWORK_ERROR,
      details: error,
    };
  }

  // 认证相关方法
  async login(username: string, password: string): Promise<ApiResponse> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async logout(): Promise<ApiResponse> {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  // 产品相关方法
  async getProducts(params?: ProductSearchParams): Promise<Product[]> {
    // 直接调用新的API，获取所有产品（最多20000个）
    const searchParams = new URLSearchParams();

    // 设置大的limit值获取所有产品
    searchParams.append('Pagination.Limit', '20000');

    // 只获取未删除的产品
    searchParams.append('IsDeleted', 'false');

    // 添加搜索参数
    if (params?.query) {
      searchParams.append('Search', params.query);
    }

    if (params?.groupId) {
      searchParams.append('GroupId', params.groupId.toString());
    }

    if (params?.inStockOnly) {
      searchParams.append('InStockOnly', 'true');
    }

    // 添加排序参数
    if (params?.sortBy) {
      const sortField = params.sortBy === 'name' ? 'Name' :
                       params.sortBy === 'price' ? 'Price' :
                       params.sortBy === 'cost' ? 'Cost' :
                       params.sortBy === 'createdAt' ? 'CreatedAt' : 'Name';

      const sortOrder = params?.sortOrder === 'desc' ? 'desc' : 'asc';
      searchParams.append('SortBy', sortField);
      searchParams.append('SortOrder', sortOrder);
    }

    const endpoint = `/products?${searchParams.toString()}`;
    const response = await this.request<any>(endpoint);

    console.log('Products API response:', response);
    console.log('Products API response.result:', response.result);
    console.log('Products API response.result type:', typeof response.result);
    if (response.result && typeof response.result === 'object') {
      console.log('Products API response.result keys:', Object.keys(response.result));
    }

    // 使用与原有系统相同的数据提取逻辑
    let products: Product[] = [];

    if (response.result) {
      // 优先检查 result.data（分页响应）
      if (response.result.data && Array.isArray(response.result.data)) {
        products = response.result.data;
      }
      // 检查 result 是否直接是数组
      else if (Array.isArray(response.result)) {
        products = response.result;
      }
    }

    console.log(`All products loaded: ${products.length} products`);

    // API已经过滤了删除的产品（IsDeleted=false），直接返回
    return products;
  }

  async getProduct(id: number): Promise<Product> {
    return this.request<Product>(`/products/${id}`);
  }

  async createProduct(productData: Partial<Product>): Promise<ApiResponse<Product>> {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(productData: Partial<Product>): Promise<ApiResponse<Product>> {
    return this.request('/products', {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: number): Promise<ApiResponse> {
    return this.request(`/products/${id}`, {
      method: 'DELETE',
    });
  }

  async getProductStock(id: number): Promise<number> {
    try {
      const response = await this.request<ApiResponse<ProductStock>>(`/productsstock?ProductId=${id}`);
      return response.result?.onHand || 0;
    } catch (error) {
      // 静默处理库存错误，返回0
      console.log(`Failed to load stock for product ${id}:`, error);
      return 0;
    }
  }

  async updateProductStock(id: number, quantity: number): Promise<ApiResponse> {
    return this.request(`/products/${id}/stock`, {
      method: 'PUT',
      body: JSON.stringify({ quantity }),
    });
  }

  // 产品组相关方法
  async getProductGroups(): Promise<ProductGroup[]> {
    const response = await this.request<any>('/productgroups?Pagination.Limit=1000');
    console.log('ProductGroups API response:', response);

    // 检查响应结构
    if (Array.isArray(response)) {
      return response;
    } else if (response.result) {
      // 检查result.data数组（分页响应）
      if (response.result.data && Array.isArray(response.result.data)) {
        return response.result.data;
      }
      // 检查result是否是数组
      else if (Array.isArray(response.result)) {
        return response.result;
      }
      // 检查result是否包含items数组（分页响应）
      else if (response.result.items && Array.isArray(response.result.items)) {
        return response.result.items;
      }
      // 检查result本身是否包含产品组数据
      else if (typeof response.result === 'object') {
        // 可能是分页对象，查找产品组数组
        const possibleArrays = Object.values(response.result).filter(Array.isArray);
        if (possibleArrays.length > 0) {
          return possibleArrays[0] as ProductGroup[];
        }
      }
    }

    console.error('Unexpected ProductGroups API response structure:', response);
    return [];
  }

  async createProductGroup(groupData: Partial<ProductGroup>): Promise<ApiResponse<ProductGroup>> {
    return this.request('/productgroups', {
      method: 'POST',
      body: JSON.stringify(groupData),
    });
  }

  // 产品图片相关方法
  async updateProductImage(imageData: ProductImage): Promise<ApiResponse> {
    return this.request('/products/images', {
      method: 'PUT',
      body: JSON.stringify(imageData),
    });
  }

  // 用户价格相关方法
  async getProductUserPrices(productId: number): Promise<ProductUserPrice[]> {
    const response = await this.request<ApiResponse<ProductUserPrice[]>>(`/products/${productId}/userprices`);
    return response.result || [];
  }

  async updateProductUserPrices(productId: number, userPrices: ProductUserPrice[]): Promise<ApiResponse> {
    return this.request(`/products/${productId}/userprices`, {
      method: 'PUT',
      body: JSON.stringify(userPrices),
    });
  }

  // 禁用用户组相关方法
  async getProductDisallowedUserGroups(productId: number): Promise<ProductDisallowedUserGroup[]> {
    const response = await this.request<ApiResponse<ProductDisallowedUserGroup[]>>(`/products/${productId}/disallowedusergroups`);
    return response.result || [];
  }

  async updateProductDisallowedUserGroups(productId: number, disallowedGroups: ProductDisallowedUserGroup[]): Promise<ApiResponse> {
    return this.request(`/products/${productId}/disallowedusergroups`, {
      method: 'PUT',
      body: JSON.stringify(disallowedGroups),
    });
  }

  // 购买可用性相关方法
  async updateProductPurchaseAvailability(productId: number, availabilityData: ProductPurchaseAvailability): Promise<ApiResponse> {
    return this.request(`/products/${productId}/purchaseavailability`, {
      method: 'PUT',
      body: JSON.stringify(availabilityData),
    });
  }

  // 用户组相关方法
  async getUserGroups(): Promise<UserGroup[]> {
    const response = await this.request<ApiResponse<UserGroup[]>>('/usergroups');
    return response.result || [];
  }
}

// 创建并导出API客户端实例
export const apiClient = new ApiClient();

// 导出便捷方法（保持this绑定）
export const login = (username: string, password: string) => apiClient.login(username, password);
export const logout = () => apiClient.logout();
export const getProducts = (params?: ProductSearchParams) => apiClient.getProducts(params);
export const getProduct = (id: number) => apiClient.getProduct(id);
export const createProduct = (productData: Partial<Product>) => apiClient.createProduct(productData);
export const updateProduct = (productData: Partial<Product>) => apiClient.updateProduct(productData);
export const deleteProduct = (id: number) => apiClient.deleteProduct(id);
export const getProductStock = (id: number) => apiClient.getProductStock(id);
export const updateProductStock = (id: number, quantity: number) => apiClient.updateProductStock(id, quantity);
export const getProductGroups = () => apiClient.getProductGroups();
export const createProductGroup = (groupData: Partial<ProductGroup>) => apiClient.createProductGroup(groupData);
export const updateProductImage = (imageData: ProductImage) => apiClient.updateProductImage(imageData);
export const getProductUserPrices = (productId: number) => apiClient.getProductUserPrices(productId);
export const updateProductUserPrices = (productId: number, userPrices: ProductUserPrice[]) => apiClient.updateProductUserPrices(productId, userPrices);
export const getProductDisallowedUserGroups = (productId: number) => apiClient.getProductDisallowedUserGroups(productId);
export const updateProductDisallowedUserGroups = (productId: number, disallowedGroups: ProductDisallowedUserGroup[]) => apiClient.updateProductDisallowedUserGroups(productId, disallowedGroups);
export const updateProductPurchaseAvailability = (productId: number, availabilityData: ProductPurchaseAvailability) => apiClient.updateProductPurchaseAvailability(productId, availabilityData);
export const getUserGroups = () => apiClient.getUserGroups();
