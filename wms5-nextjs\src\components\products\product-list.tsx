'use client';

import { useState, useEffect } from 'react';
import { Plus, RefreshCw, Package } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ProductCard } from './product-card';
import { ProductFilters } from './product-filters';
import { QuickEditModal } from './quick-edit-modal';
import { AdvancedEditModal } from './advanced-edit-modal';
import { Loading, PageLoading } from '@/components/ui/loading';
import { ErrorDisplay, PageError } from '@/components/ui/error-display';
import { getProducts, getProductGroups, getProductStock } from '@/lib/api';
import type { Product, ProductGroup, ProductSearchParams } from '@/types';

export function ProductList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [stockData, setStockData] = useState<Record<number, number>>({});
  const [filters, setFilters] = useState<ProductSearchParams>({
    query: '',
    groupId: undefined,
    inStockOnly: false,
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortOrder: 'asc',
  });
  const [allProducts, setAllProducts] = useState<Product[]>([]); // 存储所有产品用于前端搜索和排序
  const [displayProducts, setDisplayProducts] = useState<Product[]>([]); // 当前显示的产品

  // 模态框状态
  const [quickEditProduct, setQuickEditProduct] = useState<Product | null>(null);
  const [advancedEditProduct, setAdvancedEditProduct] = useState<Product | null>(null);
  const [isQuickEditOpen, setIsQuickEditOpen] = useState(false);
  const [isAdvancedEditOpen, setIsAdvancedEditOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState('');
  const [hasMore, setHasMore] = useState(true);

  // 加载产品组
  const loadProductGroups = async () => {
    try {
      const groups = await getProductGroups();
      setProductGroups(groups);
    } catch (err: any) {
      console.error('Failed to load product groups:', err);
      // 产品组加载失败不阻止主要功能
    }
  };

  // 加载产品列表（只在选择产品组时调用，获取该组的所有产品）
  const loadProducts = async (groupId: number) => {
    try {
      setIsLoading(true);
      setError('');

      // 获取该产品组的所有产品
      const productList = await getProducts({ groupId, limit: 20000 });

      setAllProducts(productList);
      setDisplayProducts(productList); // 初始显示所有产品
      setProducts(productList); // 保持兼容性

      // 加载库存数据
      await loadStockData(productList);

    } catch (err: any) {
      console.error('Failed to load products:', err);
      setError(err.message || '加载产品失败');
      setAllProducts([]);
      setDisplayProducts([]);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载库存数据
  const loadStockData = async (productList: Product[]) => {
    const stockPromises = productList
      .filter(product => !product.isStockDisabled)
      .map(async (product) => {
        try {
          const stock = await getProductStock(product.id);
          return { productId: product.id, stock };
        } catch (err) {
          console.error(`Failed to load stock for product ${product.id}:`, err);
          return { productId: product.id, stock: 0 };
        }
      });

    const stockResults = await Promise.all(stockPromises);
    const newStockData: Record<number, number> = {};
    
    stockResults.forEach(({ productId, stock }) => {
      newStockData[productId] = stock;
    });

    setStockData(prev => ({ ...prev, ...newStockData }));
  };

  // 初始化加载
  useEffect(() => {
    const initialize = async () => {
      await loadProductGroups();
      // 不在初始化时加载产品，等用户选择产品组后再加载
      setIsLoading(false); // 设置加载状态为false
    };

    initialize();
  }, []);

  // 前端搜索和排序逻辑
  const applyFiltersAndSort = () => {
    let filteredProducts = [...allProducts];

    // 1. 应用搜索过滤
    if (filters.query) {
      const keyword = filters.query.toLowerCase().trim();
      filteredProducts = filteredProducts.filter(product => {
        const productName = product.name.toLowerCase();
        const barcode = (product.barcode || '').toLowerCase();
        // 产品名称包含关键词或条码以关键词开头
        return productName.includes(keyword) || barcode.startsWith(keyword);
      });
    }

    // 2. 应用库存过滤
    if (filters.inStockOnly) {
      filteredProducts = filteredProducts.filter(product => {
        const stock = stockData[product.id] || 0;
        return stock > 0;
      });
    }

    // 3. 应用排序
    if (filters.sortBy) {
      filteredProducts.sort((a, b) => {
        const isDesc = filters.sortOrder === 'desc';

        switch (filters.sortBy) {
          case 'name':
            const nameCompare = a.name.localeCompare(b.name, 'zh-CN', { numeric: true });
            return isDesc ? -nameCompare : nameCompare;
          case 'price':
            const priceA = parseFloat(String(a.price || 0)) || 0;
            const priceB = parseFloat(String(b.price || 0)) || 0;
            return isDesc ? priceB - priceA : priceA - priceB;
          case 'cost':
            const costA = parseFloat(String(a.cost || 0)) || 0;
            const costB = parseFloat(String(b.cost || 0)) || 0;
            return isDesc ? costB - costA : costA - costB;
          case 'createdAt':
            const dateA = new Date(a.createdAt || 0).getTime();
            const dateB = new Date(b.createdAt || 0).getTime();
            return isDesc ? dateB - dateA : dateA - dateB;
          default:
            return 0;
        }
      });
    }

    setDisplayProducts(filteredProducts);
    setProducts(filteredProducts); // 保持兼容性
  };

  // 当产品组改变时加载产品
  useEffect(() => {
    if (filters.groupId) {
      loadProducts(filters.groupId);
    } else {
      setAllProducts([]);
      setDisplayProducts([]);
      setProducts([]);
      setStockData({});
    }
  }, [filters.groupId]);

  // 当搜索、排序、库存过滤条件改变时重新过滤和排序
  useEffect(() => {
    if (allProducts.length > 0) {
      applyFiltersAndSort();
    }
  }, [filters.query, filters.inStockOnly, filters.sortBy, filters.sortOrder, allProducts, stockData]);

  // 处理筛选变化
  const handleFiltersChange = (newFilters: ProductSearchParams) => {
    setFilters(newFilters);
  };

  // 刷新数据
  const handleRefresh = () => {
    if (filters.groupId) {
      loadProducts(filters.groupId);
    }
  };

  // 处理产品编辑
  const handleProductEdit = (product: Product) => {
    const editType = (product as any)._editType;

    if (editType === 'advanced') {
      // 移除特殊标记
      const cleanProduct = { ...product };
      delete (cleanProduct as any)._editType;

      setAdvancedEditProduct(cleanProduct);
      setIsAdvancedEditOpen(true);
    } else {
      // 快速编辑
      setQuickEditProduct(product);
      setIsQuickEditOpen(true);
    }
  };

  // 处理产品删除
  const handleProductDelete = (productId: number) => {
    // TODO: 实现产品删除功能
    console.log('Delete product:', productId);
  };

  // 处理库存更新
  const handleStockUpdate = (productId: number, newStock: number) => {
    setStockData(prev => ({ ...prev, [productId]: newStock }));
  };

  // 保存产品编辑
  const handleSaveProduct = async (updatedProduct: Partial<Product>) => {
    try {
      const response = await fetch('/api/products', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedProduct),
      });

      if (!response.ok) {
        throw new Error('保存产品失败');
      }

      // 更新本地产品数据
      const updateProductInList = (products: Product[]) =>
        products.map(p => p.id === updatedProduct.id ? { ...p, ...updatedProduct } : p);

      setAllProducts(updateProductInList);
      setDisplayProducts(updateProductInList);
      setProducts(updateProductInList);

      // 如果产品组改变了，重新加载产品
      if (updatedProduct.groupId && updatedProduct.groupId !== filters.groupId) {
        if (filters.groupId) {
          loadProducts(filters.groupId);
        }
      }

    } catch (error: any) {
      console.error('Save product error:', error);
      throw error;
    }
  };

  if (isLoading && products.length === 0) {
    return <PageLoading text="加载产品列表中..." />;
  }

  if (error && products.length === 0) {
    return <PageError error={error} onRetry={handleRefresh} />;
  }

  const totalUnits = displayProducts.reduce((total, product) => total + (stockData[product.id] || 0), 0);

  return (
    <div className="h-full flex flex-col">
      {/* 固定的页面标题 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">产品库存管理</h1>
          <div className="flex items-center space-x-4">
            <Link href="/products/add">
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                添加产品
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={() => {
                localStorage.removeItem('token');
                window.location.href = '/login';
              }}
            >
              退出
            </Button>
          </div>
        </div>
      </div>

      {/* 固定的筛选器 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <ProductFilters
          productGroups={productGroups}
          filters={filters}
          onFiltersChange={handleFiltersChange}
          isLoading={isLoading}
        />
      </div>

      {/* 固定的统计信息 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-2">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {displayProducts.length} items
          </div>
          <div className="text-sm text-gray-600">
            {totalUnits} units
          </div>
        </div>
      </div>

      {/* 可滚动的产品列表区域 */}
      <div className="flex-1 overflow-y-auto px-6 py-4">

      {/* 错误提示 */}
      {error && displayProducts.length > 0 && (
        <ErrorDisplay error={error} onRetry={handleRefresh} />
      )}

      {/* 产品列表或空状态 */}
      {displayProducts.length === 0 && !isLoading ? (
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="h-8 w-8 text-orange-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到产品</h3>
          <p className="text-gray-500 mb-4">
            从上方开始搜索产品
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {displayProducts.map((product, index) => (
            <ProductCard
              key={`${product.id}-${index}`}
              product={product}
              stock={stockData[product.id] || 0}
              productGroups={productGroups}
              onEdit={handleProductEdit}
              onDelete={handleProductDelete}
              onStockUpdate={handleStockUpdate}
            />
          ))}
        </div>
      )}
      </div>

      {/* 快速编辑模态框 */}
      <QuickEditModal
        product={quickEditProduct}
        isOpen={isQuickEditOpen}
        onClose={() => {
          setIsQuickEditOpen(false);
          setQuickEditProduct(null);
        }}
        onSave={handleSaveProduct}
      />

      {/* 高级编辑模态框 */}
      <AdvancedEditModal
        product={advancedEditProduct}
        productGroups={productGroups}
        isOpen={isAdvancedEditOpen}
        onClose={() => {
          setIsAdvancedEditOpen(false);
          setAdvancedEditProduct(null);
        }}
        onSave={handleSaveProduct}
      />
    </div>
  );
}
