import { NextRequest, NextResponse } from 'next/server';
import { clearAuthCookie } from '@/lib/auth';
import { HTTP_STATUS, SUCCESS_MESSAGES } from '@/lib/constants';

export async function POST(request: NextRequest) {
  try {
    // 清除认证<PERSON>ie
    await clearAuthCookie();

    return NextResponse.json(
      {
        success: true,
        message: SUCCESS_MESSAGES.LOGOUT_SUCCESS,
      },
      { status: HTTP_STATUS.OK }
    );

  } catch (error: any) {
    console.error('Logout error:', error);
    
    return NextResponse.json(
      { error: '退出登录失败' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
