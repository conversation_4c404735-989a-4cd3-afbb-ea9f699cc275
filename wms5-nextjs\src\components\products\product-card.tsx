'use client';

import { useState } from 'react';
import { Package, Edit, Trash2, MoreVertical } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import type { Product, ProductGroup } from '@/types';
import { cn } from '@/lib/utils';

interface ProductCardProps {
  product: Product;
  stock?: number;
  productGroups?: ProductGroup[];
  onEdit?: (product: Product) => void;
  onDelete?: (productId: number) => void;
  onStockUpdate?: (productId: number, newStock: number) => void;
  onClick?: (product: Product) => void;
}

export function ProductCard({
  product,
  stock = 0,
  productGroups = [],
  onEdit,
  onDelete,
  onStockUpdate,
  onClick,
}: ProductCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [stockInput, setStockInput] = useState('1');
  const [isUpdatingStock, setIsUpdatingStock] = useState(false);

  const handleCardClick = () => {
    if (onClick) {
      onClick(product);
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(product);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(product.id);
  };

  // 库存修改API调用
  const updateStock = async (type: number) => {
    const amount = parseInt(stockInput) || 1;
    if (amount <= 0) {
      alert('请输入有效的数量');
      return;
    }

    setIsUpdatingStock(true);
    try {
      const response = await fetch('/api/productsstock', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: product.id,
          type: type, // 0: 增加, 1: 减少
          amount: amount,
        }),
      });

      if (!response.ok) {
        throw new Error('库存修改失败');
      }

      const data = await response.json();
      console.log('Stock update response:', data);

      // 更新本地库存显示
      const newStock = type === 0 ? stock + amount : Math.max(0, stock - amount);
      onStockUpdate?.(product.id, newStock);

      // 清空输入框
      setStockInput('1');

    } catch (error: any) {
      console.error('Stock update error:', error);
      alert(error.message || '库存修改失败');
    } finally {
      setIsUpdatingStock(false);
    }
  };

  const handleStockIncrease = (e: React.MouseEvent) => {
    e.stopPropagation();
    updateStock(0); // 增加库存
  };

  const handleStockDecrease = (e: React.MouseEvent) => {
    e.stopPropagation();
    updateStock(1); // 减少库存
  };

  const getStockStatus = () => {
    if (product.isStockDisabled) {
      return { label: '库存已禁用', variant: 'secondary' as const, className: 'text-gray-600 bg-gray-100' };
    }
    if (stock <= 0) {
      return { label: '缺货', variant: 'destructive' as const, className: 'text-red-600 bg-red-50' };
    }
    if (product.minimumStock && stock <= product.minimumStock) {
      return { label: '库存不足', variant: 'secondary' as const, className: 'text-yellow-600 bg-yellow-50' };
    }
    return { label: '有库存', variant: 'default' as const, className: 'text-green-600 bg-green-50' };
  };

  const stockStatus = getStockStatus();
  const priceText = product.price ? `RM ${product.price.toFixed(2)}` : 'N/A';
  const costText = product.cost ? `RM ${product.cost.toFixed(2)}` : 'N/A';
  const groupName = product.groupId
    ? productGroups.find(g => g.id === product.groupId)?.name || 'N/A'
    : 'N/A';

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        {/* 左侧：产品图标和信息 */}
        <div className="flex items-center space-x-4 flex-1">
          {/* 产品图标 */}
          <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <Package className="h-6 w-6 text-orange-600" />
          </div>

          {/* 产品信息 */}
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-lg mb-1">
              {product.name}
            </h3>
            <div className="text-sm text-gray-600">
              {priceText} / {costText} / {product.barcode || 'N/A'} / {groupName}
            </div>
          </div>
        </div>

        {/* 右侧：库存数量和操作按钮 */}
        <div className="flex items-center space-x-4">
          {/* 库存数量 */}
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {product.isStockDisabled ? '-' : stock}
            </div>
            <div className="text-xs text-gray-500">UNITS</div>
          </div>

          {/* 库存状态标签 */}
          <div className="text-sm text-gray-600">
            {stockStatus.label}
          </div>

          {/* 库存输入框 */}
          <div className="flex items-center space-x-1">
            <Input
              type="number"
              min="1"
              value={stockInput}
              onChange={(e) => setStockInput(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              className="w-16 h-8 text-center text-sm"
              disabled={isUpdatingStock || product.isStockDisabled}
            />
          </div>

          {/* 操作按钮组 */}
          <div className="flex items-center space-x-2">
            {/* 增加库存按钮 */}
            <Button
              size="sm"
              className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 text-xs"
              onClick={handleStockIncrease}
              disabled={isUpdatingStock || product.isStockDisabled}
            >
              +
            </Button>

            {/* 减少库存按钮 */}
            <Button
              size="sm"
              className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 text-xs"
              onClick={handleStockDecrease}
              disabled={isUpdatingStock || product.isStockDisabled}
            >
              -
            </Button>

            {/* 快速修改按钮 */}
            <Button
              size="sm"
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.(product);
              }}
            >
              快速修改
            </Button>

            {/* 高级编辑按钮 */}
            <Button
              size="sm"
              className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                // 使用特殊标记来区分高级编辑
                onEdit?.({ ...product, _editType: 'advanced' } as any);
              }}
            >
              高级编辑
            </Button>
          </div>
        </div>
      </div>

      {/* 展开的详细信息 */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">产品ID:</span>
              <span className="ml-2 text-gray-600">{product.id}</span>
            </div>
            {product.description && (
              <div className="col-span-2">
                <span className="font-medium text-gray-700">描述:</span>
                <p className="mt-1 text-gray-600">{product.description}</p>
              </div>
            )}
            {product.minimumStock && (
              <div>
                <span className="font-medium text-gray-700">最小库存:</span>
                <span className="ml-2 text-gray-600">{product.minimumStock}</span>
              </div>
            )}
            {product.maximumStock && (
              <div>
                <span className="font-medium text-gray-700">最大库存:</span>
                <span className="ml-2 text-gray-600">{product.maximumStock}</span>
              </div>
            )}
            <div>
              <span className="font-medium text-gray-700">库存警告:</span>
              <span className="ml-2 text-gray-600">
                {product.stockAlert ? '启用' : '禁用'}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">缺货禁售:</span>
              <span className="ml-2 text-gray-600">
                {product.disallowSaleIfOutOfStock ? '是' : '否'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
