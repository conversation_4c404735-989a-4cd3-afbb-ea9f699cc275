'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, X, Save } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Accordion, AccordionItem } from '@/components/ui/accordion';
import { LoadingOverlay } from '@/components/ui/loading';
import { getProductGroups, createProduct } from '@/lib/api';
import type { ProductGroup, ProductFormData } from '@/types';

export function ProductForm() {
  const router = useRouter();
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    price: undefined,
    cost: undefined,
    barcode: '',
    groupId: undefined,
    description: '',
    enableStock: true,
    minimumStock: undefined,
    maximumStock: undefined,
    disallowSaleIfOutOfStock: false,
    stockAlert: false,
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // 加载产品组
  useEffect(() => {
    const loadProductGroups = async () => {
      try {
        setIsLoading(true);
        const groups = await getProductGroups();
        setProductGroups(groups);
      } catch (err: any) {
        console.error('Failed to load product groups:', err);
        setError('加载产品组失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadProductGroups();
  }, []);

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    // 清除错误信息
    if (error) setError('');
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        setError('请选择图片文件');
        return;
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('图片文件大小不能超过5MB');
        return;
      }

      setFormData(prev => ({ ...prev, image: file }));

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({ ...prev, image: undefined }));
    setImagePreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证必填字段
    if (!formData.name.trim()) {
      setError('请输入产品名称');
      return;
    }

    if (!formData.groupId) {
      setError('请选择产品组');
      return;
    }

    setIsSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const productData = {
        productGroupId: formData.groupId, // 使用原API的字段名
        name: formData.name.trim(),
        description: formData.description?.trim() || "",
        price: formData.price || 0,
        cost: formData.cost || 0,
        barcode: formData.barcode?.trim() || "",
        enableStock: formData.enableStock,
        disallowSaleIfOutOfStock: formData.disallowSaleIfOutOfStock || false,
        stockAlert: formData.stockAlert || false,
        isDeleted: false, // 确保产品不被标记为已删除
      };

      const result = await createProduct(productData);
      
      if (result.result?.id) {
        setSuccess('产品创建成功！');
        
        // 2秒后跳转到产品列表
        setTimeout(() => {
          router.push('/products');
        }, 2000);
      } else {
        setError('创建产品失败');
      }
    } catch (err: any) {
      console.error('Create product error:', err);
      setError(err.message || '创建产品失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LoadingOverlay isLoading={isLoading} text="加载中...">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/products">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回产品列表
              </Button>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">添加产品</h1>
          </div>
        </div>

        {/* 错误和成功提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          <Accordion>
            {/* GENERAL 部分 */}
            <AccordionItem title="GENERAL" defaultExpanded>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* 表单字段 */}
                <div className="lg:col-span-2 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">产品名称 *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="输入产品名称"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="groupId">产品组 *</Label>
                      <Select
                        value={formData.groupId?.toString() || ''}
                        onValueChange={(value) => handleInputChange('groupId', parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="请选择产品组" />
                        </SelectTrigger>
                        <SelectContent>
                          {productGroups.map((group) => (
                            <SelectItem key={group.id} value={group.id.toString()}>
                              {group.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">产品描述</Label>
                    <Textarea
                      id="description"
                      value={formData.description || ''}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="输入产品描述..."
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="price">销售价格 (RM)</Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.price || ''}
                        onChange={(e) => handleInputChange('price', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="0.00"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="cost">成本价格 (RM)</Label>
                      <Input
                        id="cost"
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.cost || ''}
                        onChange={(e) => handleInputChange('cost', e.target.value ? parseFloat(e.target.value) : undefined)}
                        placeholder="0.00"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="barcode">条码</Label>
                      <Input
                        id="barcode"
                        value={formData.barcode || ''}
                        onChange={(e) => handleInputChange('barcode', e.target.value)}
                        placeholder="输入条码"
                      />
                    </div>
                  </div>
                </div>

                {/* 图片上传 */}
                <div className="space-y-4">
                  <Label>产品图片</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    {imagePreview ? (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="产品预览"
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeImage}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                        <div className="text-sm text-gray-600">
                          <p>400px × 400px</p>
                          <p>点击或拖拽上传图片</p>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          id="image-upload"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById('image-upload')?.click()}
                        >
                          选择图片
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </AccordionItem>

            {/* STOCK 部分 */}
            <AccordionItem title="STOCK">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableStock"
                    checked={formData.enableStock}
                    onCheckedChange={(checked) => handleInputChange('enableStock', checked)}
                  />
                  <Label htmlFor="enableStock">启用库存管理</Label>
                </div>

                {formData.enableStock && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minimumStock">最小库存</Label>
                      <Input
                        id="minimumStock"
                        type="number"
                        min="0"
                        value={formData.minimumStock || ''}
                        onChange={(e) => handleInputChange('minimumStock', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="0"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maximumStock">最大库存</Label>
                      <Input
                        id="maximumStock"
                        type="number"
                        min="0"
                        value={formData.maximumStock || ''}
                        onChange={(e) => handleInputChange('maximumStock', e.target.value ? parseInt(e.target.value) : undefined)}
                        placeholder="0"
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="disallowSaleIfOutOfStock"
                      checked={formData.disallowSaleIfOutOfStock}
                      onCheckedChange={(checked) => handleInputChange('disallowSaleIfOutOfStock', checked)}
                    />
                    <Label htmlFor="disallowSaleIfOutOfStock">缺货时禁止销售</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="stockAlert"
                      checked={formData.stockAlert}
                      onCheckedChange={(checked) => handleInputChange('stockAlert', checked)}
                    />
                    <Label htmlFor="stockAlert">启用库存警告</Label>
                  </div>
                </div>
              </div>
            </AccordionItem>
          </Accordion>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4 mt-6">
            <Link href="/products">
              <Button type="button" variant="outline">
                取消
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  创建中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  创建产品
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </LoadingOverlay>
  );
}
