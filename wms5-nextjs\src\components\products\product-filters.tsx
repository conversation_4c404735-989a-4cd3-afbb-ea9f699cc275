'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { ProductGroup, ProductSearchParams } from '@/types';

interface ProductFiltersProps {
  productGroups: ProductGroup[];
  filters: ProductSearchParams;
  onFiltersChange: (filters: ProductSearchParams) => void;
  isLoading?: boolean;
}

export function ProductFilters({
  productGroups,
  filters,
  onFiltersChange,
  isLoading = false,
}: ProductFiltersProps) {
  const [searchQuery, setSearchQuery] = useState(filters.query || '');

  const handleSearch = () => {
    onFiltersChange({ ...filters, query: searchQuery, page: 1 });
  };

  const handleGroupChange = (groupId: string) => {
    const newGroupId = groupId === 'all' ? undefined : parseInt(groupId);
    onFiltersChange({ ...filters, groupId: newGroupId, page: 1 });
  };

  const handleInStockChange = (checked: boolean) => {
    onFiltersChange({ ...filters, inStockOnly: checked, page: 1 });
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split('-') as [any, 'asc' | 'desc'];
    onFiltersChange({ ...filters, sortBy, sortOrder, page: 1 });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex flex-wrap items-center gap-4">
        {/* 产品组选择 */}
        <div className="flex items-center space-x-2">
          <Select
            value={filters.groupId?.toString() || 'all'}
            onValueChange={handleGroupChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-32 h-8 text-sm">
              <SelectValue placeholder="产品组" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">产品组</SelectItem>
              {productGroups.map((group) => (
                <SelectItem key={group.id} value={group.id.toString()}>
                  {group.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 搜索框 */}
        <div className="flex-1 min-w-64">
          <Input
            placeholder="Search by name, barcode..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="h-8 text-sm"
            disabled={isLoading}
          />
        </div>

        {/* 仅库存复选框 */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="inStock"
            checked={filters.inStockOnly}
            onCheckedChange={handleInStockChange}
            disabled={isLoading}
          />
          <label
            htmlFor="inStock"
            className="text-sm text-gray-700 cursor-pointer"
          >
            仅库存
          </label>
        </div>

        {/* 排序选择 */}
        <div className="flex items-center space-x-2">
          <Select
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onValueChange={handleSortChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-24 h-8 text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name-asc">产品名称 A→Z</SelectItem>
              <SelectItem value="name-desc">产品名称 Z→A</SelectItem>
              <SelectItem value="price-desc">价格 高→低</SelectItem>
              <SelectItem value="price-asc">价格 低→高</SelectItem>
              <SelectItem value="createdAt-desc">ID 新→旧</SelectItem>
              <SelectItem value="createdAt-asc">ID 旧→新</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 立即搜索按钮 */}
        <Button
          onClick={handleSearch}
          disabled={isLoading}
          className="h-8 px-4 text-sm bg-blue-500 hover:bg-blue-600"
        >
          立即搜索
        </Button>
      </div>
    </div>
  );
}
