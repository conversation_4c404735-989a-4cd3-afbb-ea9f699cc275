import { NextRequest, NextResponse } from 'next/server';
import { getAuthHeaders } from '@/lib/auth';
import { API_CONFIG, API_ENDPOINTS, HTTP_STATUS } from '@/lib/constants';
import type { Product, ProductSearchParams, ApiResponse } from '@/types';

// GET /api/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);

    // 构建新的API URL参数
    const apiSearchParams = new URLSearchParams();

    // 设置大的limit值获取所有产品
    apiSearchParams.append('Pagination.Limit', '20000');

    // 只获取未删除的产品
    apiSearchParams.append('IsDeleted', 'false');

    // 添加其他搜索参数
    if (searchParams.get('Search')) {
      apiSearchParams.append('Search', searchParams.get('Search')!);
    }
    if (searchParams.get('GroupId')) {
      apiSearchParams.append('ProductGroupId', searchParams.get('GroupId')!);
    }
    if (searchParams.get('InStockOnly')) {
      apiSearchParams.append('InStockOnly', searchParams.get('InStockOnly')!);
    }
    if (searchParams.get('SortBy')) {
      apiSearchParams.append('SortBy', searchParams.get('SortBy')!);
    }
    if (searchParams.get('SortOrder')) {
      apiSearchParams.append('SortOrder', searchParams.get('SortOrder')!);
    }

    // 构建API URL
    const apiUrl = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.PRODUCTS.LIST}?${apiSearchParams.toString()}`;

    console.log('Fetching products from:', apiUrl);

    // 获取认证头
    const authHeaders = await getAuthHeaders();

    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: authHeaders,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse<Product[]> = await response.json();
    
    return NextResponse.json(data, { status: HTTP_STATUS.OK });

  } catch (error: any) {
    console.error('Get products error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '获取产品列表失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限访问产品列表';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// POST /api/products - 创建产品
export async function POST(request: NextRequest) {
  try {
    // 获取认证头
    const authHeaders = await getAuthHeaders();
    
    // 获取请求体
    const productData: Partial<Product> = await request.json();
    
    // 构建API URL
    const apiUrl = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.PRODUCTS.CREATE}`;
    
    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse<Product> = await response.json();
    
    return NextResponse.json(data, { status: HTTP_STATUS.CREATED });

  } catch (error: any) {
    console.error('Create product error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '创建产品失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限创建产品';
    } else if (error.message?.includes('400')) {
      statusCode = HTTP_STATUS.BAD_REQUEST;
      errorMessage = '产品数据验证失败';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// PUT /api/products - 更新产品
export async function PUT(request: NextRequest) {
  try {
    // 获取认证头
    const authHeaders = await getAuthHeaders();
    
    // 获取请求体
    const productData: Partial<Product> = await request.json();
    
    // 构建API URL
    const apiUrl = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.PRODUCTS.UPDATE}`;
    
    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: {
        ...authHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse<Product> = await response.json();
    
    return NextResponse.json(data, { status: HTTP_STATUS.OK });

  } catch (error: any) {
    console.error('Update product error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '更新产品失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限更新产品';
    } else if (error.message?.includes('400')) {
      statusCode = HTTP_STATUS.BAD_REQUEST;
      errorMessage = '产品数据验证失败';
    } else if (error.message?.includes('404')) {
      statusCode = HTTP_STATUS.NOT_FOUND;
      errorMessage = '产品不存在';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
