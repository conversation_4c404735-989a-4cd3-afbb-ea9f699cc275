import { NextRequest, NextResponse } from 'next/server';
import { getAuthFromRequest, createAuthResponse } from '@/lib/auth';

// 需要认证的路径
const protectedPaths = [
  '/products',
  '/api/products',
];

// 公开路径（不需要认证）
const publicPaths = [
  '/login',
  '/api/auth/login',
  '/api/auth/logout',
];

// 静态资源路径
const staticPaths = [
  '/_next',
  '/favicon.ico',
  '/images',
  '/icons',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 跳过静态资源
  if (staticPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // 检查是否为公开路径
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));
  
  // 检查是否为受保护路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));

  // 获取认证状态
  const authData = getAuthFromRequest(request);
  const isAuthenticated = authData !== null;

  // 如果是受保护路径但用户未认证，重定向到登录页
  if (isProtectedPath && !isAuthenticated) {
    return createAuthResponse(request, '/login');
  }

  // 如果用户已认证但访问登录页，重定向到产品页面
  if (isAuthenticated && pathname === '/login') {
    return NextResponse.redirect(new URL('/products', request.url));
  }

  // 如果访问根路径，根据认证状态重定向
  if (pathname === '/') {
    if (isAuthenticated) {
      return NextResponse.redirect(new URL('/products', request.url));
    } else {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  // 对于API路由，添加认证头信息
  if (pathname.startsWith('/api/') && !isPublicPath && isAuthenticated) {
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-auth-token', authData.token);
    requestHeaders.set('x-user-id', authData.user?.id?.toString() || '');
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了以下开头的路径：
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
