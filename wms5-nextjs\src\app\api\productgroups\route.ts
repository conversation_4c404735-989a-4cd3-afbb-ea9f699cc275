import { NextRequest, NextResponse } from 'next/server';
import { getAuthHeaders } from '@/lib/auth';
import { API_CONFIG, API_ENDPOINTS, HTTP_STATUS } from '@/lib/constants';
import type { ProductGroup, ApiResponse } from '@/types';

// GET /api/productgroups - 获取产品组列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 构建API URL，添加分页参数
    const apiUrl = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.PRODUCT_GROUPS.LIST}?Pagination.Limit=1000`;

    console.log('Fetching product groups from:', apiUrl);

    // 获取认证头
    const authHeaders = await getAuthHeaders();

    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: authHeaders,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse<ProductGroup[]> = await response.json();
    
    return NextResponse.json(data, { status: HTTP_STATUS.OK });

  } catch (error: any) {
    console.error('Get product groups error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '获取产品组列表失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限访问产品组列表';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// POST /api/productgroups - 创建产品组
export async function POST(request: NextRequest) {
  try {
    // 获取认证头
    const authHeaders = await getAuthHeaders();
    
    // 获取请求体
    const groupData: Partial<ProductGroup> = await request.json();
    
    // 构建API URL
    const apiUrl = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.PRODUCT_GROUPS.CREATE}`;
    
    // 调用原始API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        ...authHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(groupData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse<ProductGroup> = await response.json();
    
    return NextResponse.json(data, { status: HTTP_STATUS.CREATED });

  } catch (error: any) {
    console.error('Create product group error:', error);
    
    let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    let errorMessage = '创建产品组失败';

    if (error.message?.includes('401')) {
      statusCode = HTTP_STATUS.UNAUTHORIZED;
      errorMessage = '认证失败，请重新登录';
    } else if (error.message?.includes('403')) {
      statusCode = HTTP_STATUS.FORBIDDEN;
      errorMessage = '没有权限创建产品组';
    } else if (error.message?.includes('400')) {
      statusCode = HTTP_STATUS.BAD_REQUEST;
      errorMessage = '产品组数据验证失败';
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
